using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IkhtibariPlatform.Models
{
    public class Payment
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Amount { get; set; }

        [Required]
        [StringLength(3)]
        public string Currency { get; set; } = "SAR";

        [Required]
        public PaymentType PaymentType { get; set; }

        [Required]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;

        [StringLength(100)]
        public string? PayLinkTransactionId { get; set; }

        [StringLength(100)]
        public string? PayLinkOrderNumber { get; set; }

        [StringLength(500)]
        public string? PayLinkUrl { get; set; }

        [StringLength(1000)]
        public string? PaymentDetails { get; set; }

        [StringLength(1000)]
        public string? FailureReason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? PaidAt { get; set; }

        public DateTime? ExpiresAt { get; set; }

        // For subscription payments
        public DateTime? SubscriptionStartDate { get; set; }
        public DateTime? SubscriptionEndDate { get; set; }
        public int? SubscriptionDurationInDays { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        // Computed properties
        public bool IsSuccessful => Status == PaymentStatus.Completed;
        public bool IsPending => Status == PaymentStatus.Pending;
        public bool IsFailed => Status == PaymentStatus.Failed || Status == PaymentStatus.Cancelled;
        public bool IsExpired => ExpiresAt.HasValue && ExpiresAt < DateTime.UtcNow;
    }

    public enum PaymentType
    {
        Subscription = 1,
        Course = 2,
        Other = 3
    }

    public enum PaymentStatus
    {
        Pending = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4,
        Refunded = 5
    }
}
