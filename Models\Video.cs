using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IkhtibariPlatform.Models
{
    public class Video
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        public string FilePath { get; set; } = string.Empty;

        public string? ThumbnailPath { get; set; }

        [Required]
        public int DurationInSeconds { get; set; }

        [Required]
        public long FileSizeInBytes { get; set; }

        [Required]
        [StringLength(10)]
        public string FileFormat { get; set; } = string.Empty;

        [Required]
        public int OrderIndex { get; set; }

        public bool IsPreview { get; set; } = false;

        public bool IsActive { get; set; } = true;

        public int ViewCount { get; set; } = 0;

        [Required]
        public int CourseId { get; set; }

        [Required]
        public string UploadedById { get; set; } = string.Empty;

        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("CourseId")]
        public virtual Course Course { get; set; } = null!;

        [ForeignKey("UploadedById")]
        public virtual ApplicationUser UploadedBy { get; set; } = null!;

        public virtual ICollection<VideoProgress> VideoProgresses { get; set; } = new List<VideoProgress>();

        // Computed properties
        public string FormattedDuration
        {
            get
            {
                var timeSpan = TimeSpan.FromSeconds(DurationInSeconds);
                if (timeSpan.Hours > 0)
                    return $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
                else
                    return $"{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
            }
        }

        public string FormattedFileSize
        {
            get
            {
                string[] sizes = { "B", "KB", "MB", "GB" };
                double len = FileSizeInBytes;
                int order = 0;
                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }
                return $"{len:0.##} {sizes[order]}";
            }
        }
    }

    public class VideoProgress
    {
        public int Id { get; set; }

        [Required]
        public int VideoId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        public int WatchedSeconds { get; set; } = 0;

        public bool IsCompleted { get; set; } = false;

        public DateTime LastWatchedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("VideoId")]
        public virtual Video Video { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        // Computed properties
        public double ProgressPercentage => Video != null && Video.DurationInSeconds > 0 
            ? Math.Min(100, (double)WatchedSeconds / Video.DurationInSeconds * 100) 
            : 0;
    }
}
