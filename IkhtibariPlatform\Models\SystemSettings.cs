using System.ComponentModel.DataAnnotations;

namespace IkhtibariPlatform.Models
{
    public class SystemSettings
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Key { get; set; } = string.Empty;

        [Required]
        public string Value { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public string? UpdatedBy { get; set; }
    }

    // Static class for commonly used settings keys
    public static class SettingsKeys
    {
        public const string RequirePaidSubscription = "RequirePaidSubscription";
        public const string AllowFreeRegistration = "AllowFreeRegistration";
        public const string CommentsEnabled = "CommentsEnabled";
        public const string SubscriptionPriceMonthly = "SubscriptionPriceMonthly";
        public const string SubscriptionPriceYearly = "SubscriptionPriceYearly";
        public const string PayLinkApiKey = "PayLinkApiKey";
        public const string PayLinkSecret = "PayLinkSecret";
        public const string PayLinkEnabled = "PayLinkEnabled";
        public const string SiteName = "SiteName";
        public const string SiteDescription = "SiteDescription";
        public const string SiteLogo = "SiteLogo";
        public const string ContactEmail = "ContactEmail";
        public const string MaxVideoSizeMB = "MaxVideoSizeMB";
        public const string MaxPdfSizeMB = "MaxPdfSizeMB";
        public const string AllowedVideoFormats = "AllowedVideoFormats";
        public const string MaintenanceMode = "MaintenanceMode";
        public const string RegistrationEnabled = "RegistrationEnabled";
    }
}
