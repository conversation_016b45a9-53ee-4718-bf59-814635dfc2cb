using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IkhtibariPlatform.Models
{
    public class Comment
    {
        public int Id { get; set; }

        [Required]
        [StringLength(2000)]
        public string Content { get; set; } = string.Empty;

        [Required]
        public int CourseId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        public int? ParentCommentId { get; set; }

        public bool IsApproved { get; set; } = true;

        public bool IsDeleted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        public DateTime? DeletedAt { get; set; }

        // Navigation properties
        [ForeignKey("CourseId")]
        public virtual Course Course { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        [ForeignKey("ParentCommentId")]
        public virtual Comment? ParentComment { get; set; }

        public virtual ICollection<Comment> Replies { get; set; } = new List<Comment>();

        // Computed properties
        public bool IsReply => ParentCommentId.HasValue;
        public int ReplyCount => Replies?.Count(r => !r.IsDeleted) ?? 0;
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.UtcNow - CreatedAt;
                if (timeSpan.TotalMinutes < 1) return "منذ لحظات";
                if (timeSpan.TotalHours < 1) return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                if (timeSpan.TotalDays < 1) return $"منذ {(int)timeSpan.TotalHours} ساعة";
                if (timeSpan.TotalDays < 30) return $"منذ {(int)timeSpan.TotalDays} يوم";
                if (timeSpan.TotalDays < 365) return $"منذ {(int)(timeSpan.TotalDays / 30)} شهر";
                return $"منذ {(int)(timeSpan.TotalDays / 365)} سنة";
            }
        }
    }
}
