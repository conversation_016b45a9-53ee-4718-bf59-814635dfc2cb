using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IkhtibariPlatform.Models
{
    public class Enrollment
    {
        public int Id { get; set; }

        [Required]
        public int CourseId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        public DateTime EnrolledAt { get; set; } = DateTime.UtcNow;

        public DateTime? CompletedAt { get; set; }

        public bool IsCompleted { get; set; } = false;

        public double ProgressPercentage { get; set; } = 0;

        public DateTime? LastAccessedAt { get; set; }

        public int? Rating { get; set; } // 1-5 stars

        [StringLength(1000)]
        public string? Review { get; set; }

        public DateTime? ReviewedAt { get; set; }

        // Navigation properties
        [ForeignKey("CourseId")]
        public virtual Course Course { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        // Computed properties
        public bool HasReviewed => Rating.HasValue && !string.IsNullOrEmpty(Review);
        public TimeSpan? StudyDuration => CompletedAt.HasValue ? CompletedAt - EnrolledAt : null;
    }
}
