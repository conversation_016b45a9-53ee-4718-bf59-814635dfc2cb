/* _content/IkhtibariPlatform/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-gcb9ghuqxq] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-gcb9ghuqxq] {
  color: #0077cc;
}

.btn-primary[b-gcb9ghuqxq] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-gcb9ghuqxq], .nav-pills .show > .nav-link[b-gcb9ghuqxq] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-gcb9ghuqxq] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-gcb9ghuqxq] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-gcb9ghuqxq] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-gcb9ghuqxq] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-gcb9ghuqxq] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
