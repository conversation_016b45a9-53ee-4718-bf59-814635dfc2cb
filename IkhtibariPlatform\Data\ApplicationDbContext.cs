using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using IkhtibariPlatform.Models;

namespace IkhtibariPlatform.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Course> Courses { get; set; }
        public DbSet<Video> Videos { get; set; }
        public DbSet<PdfFile> PdfFiles { get; set; }
        public DbSet<Enrollment> Enrollments { get; set; }
        public DbSet<Comment> Comments { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<CourseCategory> CourseCategories { get; set; }
        public DbSet<VideoProgress> VideoProgresses { get; set; }
        public DbSet<SystemSettings> SystemSettings { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure CourseCategory many-to-many relationship
            builder.Entity<CourseCategory>()
                .HasKey(cc => new { cc.CourseId, cc.CategoryId });

            builder.Entity<CourseCategory>()
                .HasOne(cc => cc.Course)
                .WithMany(c => c.CourseCategories)
                .HasForeignKey(cc => cc.CourseId);

            builder.Entity<CourseCategory>()
                .HasOne(cc => cc.Category)
                .WithMany(c => c.CourseCategories)
                .HasForeignKey(cc => cc.CategoryId);

            // Configure Comment self-referencing relationship
            builder.Entity<Comment>()
                .HasOne(c => c.ParentComment)
                .WithMany(c => c.Replies)
                .HasForeignKey(c => c.ParentCommentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Course relationships
            builder.Entity<Course>()
                .HasOne(c => c.CreatedBy)
                .WithMany(u => u.CreatedCourses)
                .HasForeignKey(c => c.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Video relationships
            builder.Entity<Video>()
                .HasOne(v => v.Course)
                .WithMany(c => c.Videos)
                .HasForeignKey(v => v.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<Video>()
                .HasOne(v => v.UploadedBy)
                .WithMany()
                .HasForeignKey(v => v.UploadedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure PdfFile relationships
            builder.Entity<PdfFile>()
                .HasOne(p => p.Course)
                .WithMany(c => c.PdfFiles)
                .HasForeignKey(p => p.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<PdfFile>()
                .HasOne(p => p.UploadedBy)
                .WithMany()
                .HasForeignKey(p => p.UploadedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Enrollment relationships
            builder.Entity<Enrollment>()
                .HasOne(e => e.Course)
                .WithMany(c => c.Enrollments)
                .HasForeignKey(e => e.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<Enrollment>()
                .HasOne(e => e.User)
                .WithMany(u => u.Enrollments)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure Comment relationships
            builder.Entity<Comment>()
                .HasOne(c => c.Course)
                .WithMany(co => co.Comments)
                .HasForeignKey(c => c.CourseId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<Comment>()
                .HasOne(c => c.User)
                .WithMany(u => u.Comments)
                .HasForeignKey(c => c.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Payment relationships
            builder.Entity<Payment>()
                .HasOne(p => p.User)
                .WithMany(u => u.Payments)
                .HasForeignKey(p => p.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure VideoProgress relationships
            builder.Entity<VideoProgress>()
                .HasOne(vp => vp.Video)
                .WithMany(v => v.VideoProgresses)
                .HasForeignKey(vp => vp.VideoId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<VideoProgress>()
                .HasOne(vp => vp.User)
                .WithMany()
                .HasForeignKey(vp => vp.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure unique constraints
            builder.Entity<Enrollment>()
                .HasIndex(e => new { e.CourseId, e.UserId })
                .IsUnique();

            builder.Entity<VideoProgress>()
                .HasIndex(vp => new { vp.VideoId, vp.UserId })
                .IsUnique();

            builder.Entity<SystemSettings>()
                .HasIndex(s => s.Key)
                .IsUnique();

            // Configure decimal precision
            builder.Entity<Course>()
                .Property(c => c.Price)
                .HasPrecision(10, 2);

            builder.Entity<Course>()
                .Property(c => c.DiscountPrice)
                .HasPrecision(10, 2);

            builder.Entity<Payment>()
                .Property(p => p.Amount)
                .HasPrecision(10, 2);

            // Seed default data
            SeedData(builder);
        }

        private void SeedData(ModelBuilder builder)
        {
            // Seed default categories
            builder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "البرمجة", Description = "دورات البرمجة وتطوير البرمجيات", IconClass = "fas fa-code", Color = "#007bff", OrderIndex = 1 },
                new Category { Id = 2, Name = "التصميم", Description = "دورات التصميم الجرافيكي وتصميم المواقع", IconClass = "fas fa-paint-brush", Color = "#28a745", OrderIndex = 2 },
                new Category { Id = 3, Name = "التسويق", Description = "دورات التسويق الرقمي والتسويق الإلكتروني", IconClass = "fas fa-bullhorn", Color = "#ffc107", OrderIndex = 3 },
                new Category { Id = 4, Name = "الأعمال", Description = "دورات إدارة الأعمال وريادة الأعمال", IconClass = "fas fa-briefcase", Color = "#dc3545", OrderIndex = 4 }
            );

            // Seed default system settings
            builder.Entity<SystemSettings>().HasData(
                new SystemSettings { Id = 1, Key = SettingsKeys.SiteName, Value = "منصة اختباري التعليمية", Description = "اسم الموقع" },
                new SystemSettings { Id = 2, Key = SettingsKeys.SiteDescription, Value = "منصة تعليمية متقدمة لتعلم المهارات الحديثة", Description = "وصف الموقع" },
                new SystemSettings { Id = 3, Key = SettingsKeys.RequirePaidSubscription, Value = "true", Description = "هل يتطلب اشتراك مدفوع لمشاهدة المحتوى" },
                new SystemSettings { Id = 4, Key = SettingsKeys.AllowFreeRegistration, Value = "true", Description = "السماح بالتسجيل المجاني" },
                new SystemSettings { Id = 5, Key = SettingsKeys.CommentsEnabled, Value = "true", Description = "تفعيل التعليقات" },
                new SystemSettings { Id = 6, Key = SettingsKeys.SubscriptionPriceMonthly, Value = "99.00", Description = "سعر الاشتراك الشهري بالريال السعودي" },
                new SystemSettings { Id = 7, Key = SettingsKeys.SubscriptionPriceYearly, Value = "999.00", Description = "سعر الاشتراك السنوي بالريال السعودي" },
                new SystemSettings { Id = 8, Key = SettingsKeys.PayLinkEnabled, Value = "true", Description = "تفعيل بوابة الدفع PayLink" },
                new SystemSettings { Id = 9, Key = SettingsKeys.MaxVideoSizeMB, Value = "500", Description = "الحد الأقصى لحجم الفيديو بالميجابايت" },
                new SystemSettings { Id = 10, Key = SettingsKeys.MaxPdfSizeMB, Value = "50", Description = "الحد الأقصى لحجم ملف PDF بالميجابايت" },
                new SystemSettings { Id = 11, Key = SettingsKeys.AllowedVideoFormats, Value = "mp4,avi,mov,wmv", Description = "صيغ الفيديو المسموحة" },
                new SystemSettings { Id = 12, Key = SettingsKeys.RegistrationEnabled, Value = "true", Description = "تفعيل التسجيل الجديد" }
            );
        }
    }
}
