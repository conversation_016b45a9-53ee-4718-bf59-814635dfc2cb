using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IkhtibariPlatform.Models
{
    public class Course
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(2000)]
        public string? DetailedDescription { get; set; }

        public string? ThumbnailImagePath { get; set; }

        [Required]
        [Column(TypeName = "decimal(10,2)")]
        public decimal Price { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? DiscountPrice { get; set; }

        public int? DiscountPercentage { get; set; }

        public DateTime? DiscountExpiryDate { get; set; }

        [Required]
        public int DurationInHours { get; set; }

        [Required]
        public CourseLevel Level { get; set; } = CourseLevel.Beginner;

        [Required]
        public CourseStatus Status { get; set; } = CourseStatus.Draft;

        public bool RequiresPaidSubscription { get; set; } = true;

        public bool AllowComments { get; set; } = true;

        public int ViewCount { get; set; } = 0;

        public int EnrollmentCount { get; set; } = 0;

        public double AverageRating { get; set; } = 0;

        public int RatingCount { get; set; } = 0;

        [StringLength(500)]
        public string? Prerequisites { get; set; }

        [StringLength(500)]
        public string? WhatYouWillLearn { get; set; }

        [Required]
        public string CreatedById { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        public DateTime? PublishedAt { get; set; }

        // Navigation properties
        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; } = null!;

        public virtual ICollection<Video> Videos { get; set; } = new List<Video>();
        public virtual ICollection<PdfFile> PdfFiles { get; set; } = new List<PdfFile>();
        public virtual ICollection<Enrollment> Enrollments { get; set; } = new List<Enrollment>();
        public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();
        public virtual ICollection<CourseCategory> CourseCategories { get; set; } = new List<CourseCategory>();

        // Computed properties
        public decimal EffectivePrice => DiscountPrice ?? Price;
        public bool IsOnSale => DiscountPrice.HasValue && DiscountPrice < Price && 
                               (DiscountExpiryDate == null || DiscountExpiryDate > DateTime.UtcNow);
        public bool IsPublished => Status == CourseStatus.Active && PublishedAt.HasValue;
        public int TotalVideos => Videos?.Count ?? 0;
        public int TotalPdfs => PdfFiles?.Count ?? 0;
    }

    public enum CourseLevel
    {
        Beginner = 1,
        Intermediate = 2,
        Advanced = 3,
        Expert = 4
    }

    public enum CourseStatus
    {
        Draft = 1,
        Active = 2,
        Inactive = 3,
        Archived = 4
    }
}
