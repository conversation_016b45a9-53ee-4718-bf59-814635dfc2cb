using System.ComponentModel.DataAnnotations;

namespace IkhtibariPlatform.Models
{
    public class Category
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public string? IconClass { get; set; }

        public string? Color { get; set; }

        public bool IsActive { get; set; } = true;

        public int OrderIndex { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<CourseCategory> CourseCategories { get; set; } = new List<CourseCategory>();

        // Computed properties
        public int CourseCount => CourseCategories?.Count(cc => cc.Course.Status == CourseStatus.Active) ?? 0;
    }

    public class CourseCategory
    {
        public int CourseId { get; set; }
        public int CategoryId { get; set; }

        // Navigation properties
        public virtual Course Course { get; set; } = null!;
        public virtual Category Category { get; set; } = null!;
    }
}
