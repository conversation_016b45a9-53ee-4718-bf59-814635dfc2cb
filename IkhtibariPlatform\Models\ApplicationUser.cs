using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace IkhtibariPlatform.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Bio { get; set; }

        public string? ProfileImagePath { get; set; }

        [Required]
        public UserType UserType { get; set; } = UserType.Student;

        [Required]
        public SubscriptionType SubscriptionType { get; set; } = SubscriptionType.Free;

        public DateTime? SubscriptionExpiryDate { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastLoginAt { get; set; }

        // Navigation properties
        public virtual ICollection<Course> CreatedCourses { get; set; } = new List<Course>();
        public virtual ICollection<Enrollment> Enrollments { get; set; } = new List<Enrollment>();
        public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        // Computed properties
        public string FullName => $"{FirstName} {LastName}";
        public bool HasValidSubscription => SubscriptionType == SubscriptionType.Paid && 
                                          (SubscriptionExpiryDate == null || SubscriptionExpiryDate > DateTime.UtcNow);
    }

    public enum UserType
    {
        Admin = 1,
        Teacher = 2,
        Student = 3
    }

    public enum SubscriptionType
    {
        Free = 1,
        Paid = 2
    }
}
